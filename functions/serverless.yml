service: auth-clear-functions

plugins:
  - serverless-iam-roles-per-function
  - serverless-export-env
  - serverless-offline

provider:
  name: aws
  runtime: nodejs20.x
  stage: ${opt:stage, 'dev'}
  region: us-east-1
  timeout: 30
  environment:
    STAGE: ${self:provider.stage}
    # ===================================================================
    # DATABASE ENVIRONMENT VARIABLES TEMPORARILY DISABLED
    # ===================================================================
    # Database connection info has been commented out to disable database
    # functionality. To re-enable: uncomment the variables below
    # ===================================================================
    # DB_ENDPOINT: ${cf:auth-clear-infra-${self:provider.stage}.AuroraClusterEndpoint}
    # DB_HOST: ${cf:auth-clear-infra-${self:provider.stage}.AuroraClusterEndpoint}
    # DB_PORT: ${cf:auth-clear-infra-${self:provider.stage}.AuroraClusterPort}
    # DB_NAME: authcleardb
    # DB_SECRET_ARN: ${cf:auth-clear-infra-${self:provider.stage}.AuroraSecretArn}
    # These will be populated at runtime using the secret ARN
    # DB_USERNAME: ${env:DB_USERNAME, 'postgres'}
    # DB_PASSWORD: ${env:DB_PASSWORD, 'postgres'}
    # Payrix API configuration
    PAYRIX_API_URL: ${env:PAYRIX_API_URL, 'https://test-api.payrix.com'}
    # PAYRIX_PRIVATE_API_KEY: ${ssm:/auth-clear-infra/${self:provider.stage}/payrix-api-key, 'default-key'}
    PAYRIX_PRIVATE_API_KEY: ${env:PAYRIX_PRIVATE_API_KEY, 'default-key'}
    PAYRIX_PUBLIC_API_KEY: ${env:PAYRIX_PUBLIC_API_KEY, 'default-public-key'}
    # DynamoDB configuration
    PAYMENT_TOKENS_TABLE_NAME: !Ref PaymentTokensTable
  
  # ===================================================================
  # VPC CONFIGURATION TEMPORARILY DISABLED
  # ===================================================================
  # VPC configuration has been commented out since it's primarily needed
  # for database access. To re-enable: uncomment the configuration below
  # ===================================================================
  # VPC configuration from main stack
  # vpc:
  #   securityGroupIds:
  #     - ${cf:auth-clear-infra-${self:provider.stage}.LambdaSecurityGroupId}
  #   subnetIds:
  #     - ${cf:auth-clear-infra-${self:provider.stage}.PrivateSubnetA}
  #     - ${cf:auth-clear-infra-${self:provider.stage}.PrivateSubnetB}
  
  # IAM permissions
  iam:
    role:
      statements:
        # ===================================================================
        # DATABASE-RELATED IAM PERMISSIONS TEMPORARILY DISABLED
        # ===================================================================
        # Database and VPC permissions have been commented out to disable
        # database functionality. To re-enable: uncomment the permissions below
        # ===================================================================
        # - Effect: Allow
        #   Action:
        #     - secretsmanager:GetSecretValue
        #     - ssm:GetParameter
        #     - ssm:GetParameters
        #   Resource:
        #     - ${cf:auth-clear-infra-${self:provider.stage}.AuroraSecretArn}
        #     - "arn:aws:ssm:${self:provider.region}:*:parameter/*"
        # VPC permissions
        # - Effect: Allow
        #   Action:
        #     - ec2:CreateNetworkInterface
        #     - ec2:DescribeNetworkInterfaces
        #     - ec2:DeleteNetworkInterface
        #     - ec2:AssignPrivateIpAddresses
        #     - ec2:UnassignPrivateIpAddresses
        #   Resource: "*"
        - Effect: Allow
          Action:
            - ssm:GetParameter
            - ssm:GetParameters
          Resource:
            - "arn:aws:ssm:${self:provider.region}:*:parameter/*"
        # DynamoDB permissions for payment tokens
        - Effect: Allow
          Action:
            - dynamodb:PutItem
            - dynamodb:GetItem
            - dynamodb:DeleteItem
          Resource:
            - !GetAtt PaymentTokensTable.Arn

build:
  esbuild:
    bundle: true
    minify: false
    sourcemap: true
    target: node20
    format: cjs
    # Exclude AWS SDK which is included in Lambda runtime
    external:
      - '@aws-sdk/*'
      - 'pg-native'
      - '@mapbox/node-pre-gyp'
      - 'mock-aws-s3'
      - 'aws-sdk'
      - 'pg'
      - 'bcrypt'

functions:
  # ===================================================================
  # COMMENTED OUT FUNCTIONS - ONLY KEEPING ONBOARD MERCHANT
  # ===================================================================
  
  # # Token authorizer for API endpoints
  # authorizerFunction:
  #   handler: src/functions/authorizer/index.handler
  #   environment:
  #     STAGE: ${self:provider.stage}
  #   iamRoleStatements:
  #     - Effect: Allow
  #       Action:
  #         - ssm:GetParameter
  #       Resource: "arn:aws:ssm:${self:provider.region}:*:parameter/auth-clear-infra/${self:provider.stage}/*"
  #     # ===================================================================
  #     # DATABASE SECRET ACCESS TEMPORARILY DISABLED
  #     # ===================================================================
  #     # - Effect: Allow
  #     #   Action:
  #     #     - secretsmanager:GetSecretValue
  #     #   Resource: ${cf:auth-clear-infra-${self:provider.stage}.AuroraSecretArn}
  
  # listMerchants:
  #   handler: src/functions/merchants/list.handler
  #   events:
  #     - http:
  #         path: /merchants
  #         method: get
  #         cors:
  #           origin: ${self:custom.corsOrigins}
  #           headers:
  #             - Content-Type
  #             - X-Amz-Date
  #             - Authorization
  #             - X-Api-Key
  #             - X-Amz-Security-Token
  #             - X-Amz-User-Agent
  #           allowCredentials: true
  #           methods:
  #             - GET
  #             - OPTIONS
  #         authorizer:
  #           name: authorizerFunction
  #           type: token
  #           identitySource: method.request.header.Authorization
  #           resultTtlInSeconds: 0

  onboardMerchant:
    handler: src/functions/merchants/onboard.handler
    events:
      - http:
          path: /merchants/onboard
          method: post
          cors:
            origin: ${self:custom.corsOrigins}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true
            methods:
              - POST
              - OPTIONS
          # No authorizer - public endpoint for merchant registration



  generatePaymentConfig:
    handler: src/functions/payments/generate-payment-config.handler
    events:
      - http:
          path: /payments/generate-payment-config
          method: post
          cors:
            origin: ${self:custom.corsOrigins}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true
            methods:
              - POST
              - OPTIONS
          # No authorizer - public endpoint for payment config generation

  generateIntegrationToken:
    handler: src/functions/payments/generate-integration-token.handler
    environment:
      FRONTEND_URL: ${env:FRONTEND_URL, 'https://${cf:auth-clear-frontend-${self:provider.stage}.CloudFrontDomainName}'}
    events:
      - http:
          path: /payments/generate-integration-token
          method: post
          cors:
            origin: '*'  # Allow all origins for iframe embedding
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - POST
              - OPTIONS
          # No authorizer - public endpoint for integration token generation

  validateIframeToken:
    handler: src/functions/payments/validate-iframe-token.handler
    events:
      - http:
          path: /payments/validate-iframe-token
          method: post
          cors:
            origin: '*'  # Allow all origins for iframe embedding
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - POST
              - OPTIONS
          # No authorizer - public endpoint for iframe token validation

  iframeConfig:
    handler: src/functions/payments/iframe-config.handler
    environment:
      PAYRIX_ENVIRONMENT: ${env:PAYRIX_ENVIRONMENT, 'test'}
    events:
      - http:
          path: /payments/iframe-config
          method: get
          cors:
            origin: '*'  # Allow all origins for iframe embedding
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - GET
              - POST
              - OPTIONS
          # No authorizer - public endpoint for iframe configuration
      - http:
          path: /payments/iframe-config
          method: post
          cors:
            origin: '*'  # Allow all origins for iframe embedding
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - GET
              - POST
              - OPTIONS
          # No authorizer - public endpoint for iframe configuration

  tokenStatus:
    handler: src/functions/payments/token-status.handler
    events:
      - http:
          path: /payments/token-status
          method: get
          cors:
            origin: '*'  # Allow all origins for iframe embedding
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - GET
              - POST
              - OPTIONS
          # No authorizer - public endpoint for token status check
      - http:
          path: /payments/token-status
          method: post
          cors:
            origin: '*'  # Allow all origins for iframe embedding
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - GET
              - POST
              - OPTIONS
          # No authorizer - public endpoint for token status check

  # createAdmin:
  #   handler: src/functions/merchants/create-admin.handler
  #   environment:
  #     ADMIN_INVITE_CODE: ${ssm:/auth-clear-infra/${self:provider.stage}/admin-invite-code, 'default-code'}
  #   events:
  #     - http:
  #         path: /admin/register
  #         method: post
  #         cors:
  #           origin: ${self:custom.corsOrigins}
  #           headers:
  #             - Content-Type
  #             - X-Amz-Date
  #             - Authorization
  #             - X-Api-Key
  #             - X-Amz-Security-Token
  #             - X-Amz-User-Agent
  #           allowCredentials: true
  #           methods:
  #             - POST
  #             - OPTIONS
  #         # No authorizer - public endpoint with invite code protection

  # loginUser:
  #   handler: src/functions/merchants/login.handler
  #   events:
  #     - http:
  #         path: /auth/login
  #         method: post
  #         cors:
  #           origin: ${self:custom.corsOrigins}
  #           headers:
  #             - Content-Type
  #             - X-Amz-Date
  #             - Authorization
  #             - X-Api-Key
  #             - X-Amz-Security-Token
  #             - X-Amz-User-Agent
  #           allowCredentials: true
  #           methods:
  #             - POST
  #             - OPTIONS
  #         # No authorizer - public endpoint for authentication

custom:
  corsOrigins: "https://${cf:auth-clear-frontend-${self:provider.stage}.CloudFrontDomainName},http://localhost:3000,http://localhost:8080"
  exportEnv:
    overwrite: true
    filename: ../.env
  serverless-offline:
    stage: ${self:provider.stage}
    region: ${self:provider.region}
    # Use AWS profile for local development
    awsProfile: payrix

package:
  individually: true

resources:
  Resources:
    # DynamoDB table for payment tokens
    PaymentTokensTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: PaymentTokens-${self:provider.stage}
        AttributeDefinitions:
          - AttributeName: tokenId
            AttributeType: S
        KeySchema:
          - AttributeName: tokenId
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TimeToLiveSpecification:
          AttributeName: expiresAt
          Enabled: true
        Tags:
          - Key: Environment
            Value: ${self:provider.stage}
          - Key: Service
            Value: auth-clear-functions
          - Key: Purpose
            Value: payment-token-storage

  Outputs:
    PaymentTokensTableName:
      Description: Name of the DynamoDB table for payment tokens
      Value: !Ref PaymentTokensTable
      Export:
        Name: ${self:service}-${self:provider.stage}-PaymentTokensTableName

    PaymentTokensTableArn:
      Description: ARN of the DynamoDB table for payment tokens
      Value: !GetAtt PaymentTokensTable.Arn
      Export:
        Name: ${self:service}-${self:provider.stage}-PaymentTokensTableArn