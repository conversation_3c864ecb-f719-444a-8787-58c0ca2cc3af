import axios, { AxiosError } from "axios";
import { type OnboardingRequest } from "../functions/merchants/schemas/onboarding.schema.js";
import { logger } from "../helpers/logger.js";

interface PayrixMerchantResponse {
  id: string;
  [key: string]: any;
}

interface PayrixUserResponse {
  id: string;
  email: string;
  sanitizedUsername?: string;
  originalUsername?: string;
  [key: string]: any;
}

interface PayrixMerchantEntity {
  id: string;
  email: string;
  legal_name: string;
  dba: string;
  status: number;
  inactive: number;
  frozen: number;
  created: string;
  modified: string;
  [key: string]: any;
}

interface PayrixError {
  field: string;
  code: number;
  severity: number;
  msg: string;
  errorCode: string;
}

const PAYRIX_API_URL = process.env.PAYRIX_API_URL || "https://test-api.payrix.com";
const PAYRIX_PRIVATE_API_KEY = process.env.PAYRIX_PRIVATE_API_KEY || "c941c2f0f23137ba6ae868662e8d0bf5";

export class PayrixService {
  private apiClient = axios.create({
    baseURL: PAYRIX_API_URL,
    headers: {
      "Content-Type": "application/json",
      APIKEY: PAYRIX_PRIVATE_API_KEY, // Payrix uses APIKEY header, not Bearer
    },
  });

  async checkMerchantExists(email: string, ein?: string): Promise<boolean> {
    try {
      logger.info("Checking for existing merchant", {
        email,
        ein: ein ? "[REDACTED]" : undefined,
      });

      const response = await this.apiClient.get(`/entities`);

      logger.info("Merchant existence check response", {
        status: response.status,
        dataLength: response.data?.response?.data?.length || 0,
      });

      const merchants: PayrixMerchantEntity[] = response.data?.response?.data || [];

      const emailExists = merchants.some((merchant: PayrixMerchantEntity) => merchant.email?.toLowerCase() === email.toLowerCase());

      return emailExists;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Error checking merchant existence", {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      logger.warn("Merchant existence check failed, allowing onboarding to proceed");
      return false;
    }
  }

  async validateMerchantById(merchantId: string): Promise<{ isValid: boolean; merchant?: any; error?: string }> {
    try {
      logger.info("Validating merchant by ID", { merchantId });

      const response = await this.apiClient.get(`/merchants/${merchantId}`);

      logger.info("Merchant validation response", {
        status: response.status,
        merchantId: response.data,
      });

      const merchant = response.data?.response?.data?.[0];

      if (!merchant) {
        return {
          isValid: false,
          error: "Merchant not found",
        };
      }

      // Check if merchant is active
      // Status 1 = fully approved/active
      // Status 2 = auto-boarded (can process payments via Instant Merchant Activation)
      const isActive = (merchant.status === 1 || merchant.status === 2) && merchant.inactive === 0 && merchant.frozen === 0;

      if (!isActive) {
        const statusInfo = {
          status: merchant.status,
          inactive: merchant.inactive,
          frozen: merchant.frozen,
          autoBoarded: merchant.autoBoarded,
        };

        logger.warn("Merchant status check failed", statusInfo);

        return {
          isValid: false,
          error: `Merchant is not active (status: ${merchant.status}, inactive: ${merchant.inactive}, frozen: ${merchant.frozen})`,
        };
      }

      logger.info("Merchant validation successful", {
        merchantId,
        status: merchant.status,
        statusType: merchant.status === 1 ? "fully-approved" : merchant.status === 2 ? "auto-boarded" : "unknown",
        inactive: merchant.inactive,
        frozen: merchant.frozen,
        autoBoarded: merchant.autoBoarded,
        dba: merchant.dba,
      });

      return {
        isValid: true,
        merchant,
      };
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Error validating merchant by ID", {
        merchantId,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      // Handle specific error cases
      if (axiosError.response?.status === 404) {
        return {
          isValid: false,
          error: "Merchant not found",
        };
      }

      return {
        isValid: false,
        error: `Validation failed: ${axiosError.message}`,
      };
    }
  }

  async createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse> {
    try {
      logger.info("Sending request to Payrix API", {
        url: `${PAYRIX_API_URL}/entities`,
        headers: { "Content-Type": "application/json", APIKEY: "[REDACTED]" },
        data: merchantData,
      });

      const response = await this.apiClient.post("/entities", merchantData);

      logger.info("Payrix API response", {
        status: response.status,
        data: response.data,
      });

      // Extract entity data from Payrix response structure
      const entityData = response.data?.response?.data?.[0];
      if (!entityData) {
        throw new Error("Invalid Payrix response structure: no entity data found");
      }

      return entityData;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Payrix API Error", {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      // Re-throw with more context
      throw new Error(`Payrix API Error (${axiosError.response?.status}): ${JSON.stringify(axiosError.response?.data || axiosError.message)}`);
    }
  }

  async createUserAccount(userData: {
    username: string;
    password: string;
    first: string;
    last: string;
    email: string;
    merchantId?: string;
  }): Promise<PayrixUserResponse> {
    try {
      logger.info("Creating user account in Payrix", {
        originalUsername: userData.username,
        email: userData.email,
        merchantId: userData.merchantId,
      });

      const response = await this.apiClient.post("/logins", userData);

      logger.info("Payrix user account API response", {
        status: response.status,
        username: userData.username,
        data: response.data,
      });

      const errors: PayrixError[] = response.data?.response?.errors || [];
      if (errors.length > 0) {
        const errorMessages = errors.map((err: PayrixError) => `${err.field}: ${err.msg}`).join(", ");
        logger.error("Payrix API returned validation errors", {
          originalUsername: userData.username,
          email: userData.email,
          errors: errors,
        });
        throw new Error(`Payrix validation errors: ${errorMessages}`);
      }

      const loginData = response.data?.response?.data?.[0];
      if (!loginData) {
        logger.error("Invalid Payrix response structure: no login data found", {
          originalUsername: userData.username,
          email: userData.email,
        });
        throw new Error("Invalid Payrix response structure: no login data found");
      }

      logger.info("Payrix user account created successfully", {
        status: response.status,
        originalUsername: userData.username,
        loginId: loginData.id,
      });

      return {
        ...loginData,
        originalUsername: userData.username,
      };
    } catch (error) {
      const axiosError = error as AxiosError;

      logger.error("Payrix User Account Creation Error", {
        originalUsername: userData.username,
        email: userData.email,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      throw new Error(
        `Payrix User Account Creation Error (${axiosError.response?.status}): ${JSON.stringify(axiosError.response?.data || axiosError.message)}`
      );
    }
  }
}
