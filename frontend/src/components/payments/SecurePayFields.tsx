import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

declare global {
  interface Window {
    PayFields: {
      onSuccess?: (response: unknown) => void;
      onFailure?: (error: unknown) => void;
      onValidationFailure?: (error: unknown) => void;
      onFinish?: (response: unknown) => void;
      init: (config: unknown) => void;
      submit: () => void;
      ready: () => void;
      unmountAll?: () => void;
      config: {
        apiKey: string;
        merchant: string;
        mode: string;
        txnType: string;
        amount: number;
        description: string;
        name?: string;
        placeholders?: Record<string, string>;
        iframe?: boolean;
        responsive?: boolean;
        autoResize?: boolean;
        billing?: {
          name?: string;
          email?: string;
          phone?: string;
          address?: string;
          address2?: string;
          city?: string;
          state?: string;
          zip?: string;
          country?: string;
        };
      };
      fields?: unknown[];
      customizations: {
        style?: Record<string, Record<string, string>>;
        placeholders?: Record<string, string>;
      };
    };
  }
}

interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  returnUrl?: string;
}

interface BillingAddress {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

interface SecurePayFieldsProps {
  config: PayFieldsConfig;
  onSuccess?: (response: unknown) => void;
  onFailure?: (error: unknown) => void;
  className?: string;
  billingAddress?: BillingAddress;
}

const SecurePayFields = ({ config, onSuccess, onFailure, className = "", billingAddress }: SecurePayFieldsProps) => {
  const [loaded, setLoaded] = useState(false);
  const [scriptError, setScriptError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  const cardNumberRef = useRef<HTMLDivElement>(null);
  const cardNameRef = useRef<HTMLDivElement>(null);
  const cardCvvRef = useRef<HTMLDivElement>(null);
  const cardExpirationRef = useRef<HTMLDivElement>(null);

  // Validate config
  useEffect(() => {
    if (!config) {
      setScriptError("No payment configuration provided");
      return;
    }

    console.log("PayFields configuration received:", {
      merchantId: config.merchantId,
      amount: config.amount,
      description: config.description,
    });
  }, [config]);

  // Load PayFields script with iframe optimizations
  useEffect(() => {
    if (!config) return;

    // Check if we're in an iframe
    const isInIframe = window.self !== window.top;

    // Check if script is already loaded to prevent duplicate loading
    if (window.PayFields) {
      console.log("PayFields already loaded, initializing...");
      setLoaded(true);
      return;
    }

    const script = document.createElement("script");
    script.src = `${import.meta.env.VITE_PAYRIX_PAYMENT_URL || "https://test-api.payrix.com/payFieldsScript"}?spa=1&iframe=${isInIframe ? "1" : "0"}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      console.log("PayFields script loaded successfully", { isInIframe });
      setLoaded(true);

      // Notify parent window if in iframe
      if (isInIframe && window.parent !== window) {
        window.parent.postMessage(
          {
            type: "PAYFIELDS_SCRIPT_LOADED",
            timestamp: new Date().toISOString(),
          },
          "*"
        );
      }
    };

    script.onerror = (error) => {
      console.error("Failed to load PayFields script:", error);
      setScriptError("Failed to load payment processing script. Please try again later.");
      setIsSubmitting(false);
      toast.error("Failed to load payment processor");

      // Notify parent window of error if in iframe
      if (isInIframe && window.parent !== window) {
        window.parent.postMessage(
          {
            type: "PAYFIELDS_SCRIPT_ERROR",
            error: "Failed to load payment script",
          },
          "*"
        );
      }
    };

    document.body.appendChild(script);

    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
      if (window.PayFields && typeof window.PayFields.unmountAll === "function") {
        window.PayFields.unmountAll();
      }
    };
  }, [config]);

  // Configure PayFields
  useEffect(() => {
    if (!loaded || !window.PayFields || !config) return;

    console.log("Configuring PayFields with:", {
      merchantId: config.merchantId,
      amount: config.amount,
      mode: config.mode,
      txnType: config.txnType,
    });

    const isInIframe = window.self !== window.top;

    try {
      // Configure PayFields authentication
      window.PayFields.config.apiKey = config.publicKey;
      window.PayFields.config.merchant = config.merchantId;
      window.PayFields.config.mode = config.mode;
      window.PayFields.config.txnType = config.txnType;
      window.PayFields.config.description = config.description;

      if (config.mode === "token") {
        window.PayFields.config.amount = 0;
        if (config.txnType !== "auth") {
          console.warn("Token-only mode typically uses auth transaction type. Current type:", config.txnType);
        }
      } else {
        window.PayFields.config.amount = config.amount;
      }

      // Iframe-specific configuration
      if (isInIframe) {
        window.PayFields.config.iframe = true;
        window.PayFields.config.responsive = true;
        window.PayFields.config.autoResize = true;
      }

      // Set up form fields with correct IDs
      const fields = [
        { type: "number", element: "#card-number" },
        { type: "cvv", element: "#card-cvv" },
        { type: "expiration", element: "#card-expiration" },
      ];

      window.PayFields.fields = fields;

      // Configure styling with iframe optimizations
      const baseInputStyle = {
        display: "block",
        width: "100%",
        padding: "0.75rem 1rem",
        fontSize: "0.875rem",
        fontWeight: "400",
        lineHeight: "1.5rem",
        backgroundColor: "#fff",
        border: "1px solid #e2e8f0",
        borderRadius: "0.375rem",
        appearance: "none",
        transition: "border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out",
      };

      // Add iframe-specific styling adjustments
      if (isInIframe) {
        baseInputStyle.fontSize = "16px"; // Prevent zoom on iOS
        baseInputStyle.padding = "0.625rem 0.875rem"; // Slightly smaller for iframe
      }

      window.PayFields.customizations.style = {
        ".input": baseInputStyle,
        ".input:focus": {
          borderColor: "#3b82f6",
          outline: "none",
          boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
        },
        ".form-error": {
          color: "#e53e3e",
          fontSize: "0.75rem",
          marginTop: "0.25rem",
        },
        ".form-valid": {
          borderColor: "#10b981",
        },
      };

      // Configure placeholders with correct selectors
      window.PayFields.customizations.placeholders = {
        "#expiration": "MM/YY",
        "#payment_cvv": "CVV",
        "#payment_number": "0000 0000 0000 0000",
        "#name": "Full Name on Card",
      };

      // Add billing address to the config if provided
      if (billingAddress) {
        window.PayFields.config.billing = {
          name: `${billingAddress.firstName} ${billingAddress.lastName}`,
          email: billingAddress.email,
          phone: billingAddress.phone,
          address: billingAddress.line1,
          address2: billingAddress.line2,
          city: billingAddress.city,
          state: billingAddress.state,
          zip: billingAddress.zip,
          country: billingAddress.country,
        };
        
        // Set the cardholder name in the hidden field
        const nameField = document.getElementById('card-name');
        if (nameField) {
          // Trigger PayFields to use this name
          window.PayFields.config.name = `${billingAddress.firstName} ${billingAddress.lastName}`;
        }
      }

      // Set up callbacks with iframe communication
      window.PayFields.onSuccess = (response: any) => {
        console.log("Payment successful:", response);
        setIsSubmitting(false);
        setValidationError(null);
        toast.success("Payment processed successfully!");

        // Notify parent window if in iframe
        if (isInIframe && window.parent !== window) {
          window.parent.postMessage(
            {
              type: "PAYMENT_SUCCESS",
              data: response,
              timestamp: new Date().toISOString(),
            },
            "*"
          );
        }

        if (onSuccess) onSuccess(response);
      };

      window.PayFields.onFailure = (err: any) => {
        console.error("Payment failed:", err);
        setIsSubmitting(false);

        let errorMessage = "Payment processing failed. Please try again.";
        if (err && Array.isArray(err.errors)) {
          const fieldErrors = err.errors.map((e: any) => `${e.field}: ${e.msg}`).join(", ");
          errorMessage = `Payment failed: ${fieldErrors}`;
        } else if (err && err.message) {
          errorMessage = err.message;
        }

        setValidationError(errorMessage);
        toast.error(errorMessage);

        // Notify parent window if in iframe
        if (isInIframe && window.parent !== window) {
          window.parent.postMessage(
            {
              type: "PAYMENT_FAILURE",
              error: errorMessage,
              details: err,
              timestamp: new Date().toISOString(),
            },
            "*"
          );
        }

        if (onFailure) onFailure(err);
      };

      window.PayFields.onValidationFailure = (err: any) => {
        console.log("Validation error:", err);
        setIsSubmitting(false);
        const validationMessage = "Payment validation failed. Please check your card details.";
        setValidationError(validationMessage);
        toast.error("Please check your card details");

        // Notify parent window if in iframe
        if (isInIframe && window.parent !== window) {
          window.parent.postMessage(
            {
              type: "PAYMENT_VALIDATION_FAILURE",
              error: validationMessage,
              details: err,
              timestamp: new Date().toISOString(),
            },
            "*"
          );
        }

        if (onFailure) onFailure({ message: validationMessage, details: err });
      };

      window.PayFields.onFinish = (response: any) => {
        console.log("PayFields finished:", response);
        setIsSubmitting(false);

        // Notify parent window if in iframe
        if (isInIframe && window.parent !== window) {
          window.parent.postMessage(
            {
              type: "PAYMENT_FINISHED",
              data: response,
              timestamp: new Date().toISOString(),
            },
            "*"
          );
        }
      };

      // Initialize PayFields - this must be called last
      window.PayFields.ready();
      console.log("PayFields initialized successfully");
    } catch (error) {
      console.error("Error configuring PayFields:", error);
      setScriptError("Error configuring payment processor. Please try again later.");
      setIsSubmitting(false);
      toast.error("Payment configuration error");
    }
  }, [loaded, config, onSuccess, onFailure, billingAddress]);

  const handleSubmit = () => {
    if (!window.PayFields || isSubmitting || !config) return;

    const isInIframe = window.self !== window.top;

    console.log("Submitting secure payment...", { isInIframe });
    setIsSubmitting(true);
    setValidationError(null);

    // Notify parent window of submission start if in iframe
    if (isInIframe && window.parent !== window) {
      window.parent.postMessage(
        {
          type: "PAYMENT_SUBMISSION_STARTED",
          timestamp: new Date().toISOString(),
        },
        "*"
      );
    }

    // Set timeout for payment submission (30 seconds)
    const timeoutId = setTimeout(() => {
      if (isSubmitting) {
        console.warn("Payment submission timed out");
        setIsSubmitting(false);
        const timeoutMessage = "Payment processing timed out. Please try again.";
        toast.error(timeoutMessage);

        // Notify parent window of timeout if in iframe
        if (isInIframe && window.parent !== window) {
          window.parent.postMessage(
            {
              type: "PAYMENT_TIMEOUT",
              error: timeoutMessage,
              timestamp: new Date().toISOString(),
            },
            "*"
          );
        }

        if (onFailure) onFailure({ message: timeoutMessage });
      }
    }, 30000);

    try {
      window.PayFields.submit();
    } catch (error) {
      console.error("Error submitting payment:", error);
      clearTimeout(timeoutId);
      setIsSubmitting(false);
      const errorMessage = "Error processing payment. Please try again.";
      toast.error(errorMessage);

      // Notify parent window of error if in iframe
      if (isInIframe && window.parent !== window) {
        window.parent.postMessage(
          {
            type: "PAYMENT_SUBMISSION_ERROR",
            error: errorMessage,
            details: error,
            timestamp: new Date().toISOString(),
          },
          "*"
        );
      }

      if (onFailure) onFailure({ message: errorMessage });
    }
  };

  if (scriptError) {
    return (
      <div className={`p-4 bg-red-50 text-red-800 rounded-md ${className}`}>
        <p>{scriptError}</p>
      </div>
    );
  }

  if (!config) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-blue-600 rounded-full mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading secure payment form...</p>
      </div>
    );
  }

  return (
    <div className={`secure-payfields ${className}`}>
      {validationError && (
        <div className="p-4 mb-4 bg-red-50 text-red-800 rounded-md">
          <p>{validationError}</p>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label htmlFor="card-number" className="block mb-2 text-sm font-medium text-gray-700">
            Card Number
          </label>
          <div id="card-number" ref={cardNumberRef} className="h-12 border border-gray-300 rounded-md"></div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="card-expiration" className="block mb-2 text-sm font-medium text-gray-700">
              Expiration Date
            </label>
            <div id="card-expiration" ref={cardExpirationRef} className="h-12 border border-gray-300 rounded-md"></div>
          </div>
          <div>
            <label htmlFor="card-cvv" className="block mb-2 text-sm font-medium text-gray-700">
              CVV
            </label>
            <div id="card-cvv" ref={cardCvvRef} className="h-12 border border-gray-300 rounded-md"></div>
          </div>
        </div>

        {/* Hidden cardholder name field for PayFields */}
        <div id="card-name" ref={cardNameRef} className="hidden"></div>

        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className={`w-full py-3 px-4 font-medium rounded-md transition-all ${
            isSubmitting 
              ? "bg-gray-400 cursor-not-allowed text-white" 
              : "bg-[#364F6B] hover:bg-[#2A3F59] text-white shadow-md hover:shadow-lg"
          }`}
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : (
            `Pay $${(config.amount / 100).toFixed(2)}`
          )}
        </button>
      </div>
    </div>
  );
};

export default SecurePayFields;
